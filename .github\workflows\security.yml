name: Security

on:
  push:
    branches: [ main ]
  pull_request:
    branches: [ main ]
  schedule:
    # Run weekly on Wednesdays
    - cron: '0 6 * * 3'

jobs:
  audit:
    name: Security Audit
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - uses: dtolnay/rust-toolchain@stable
      - uses: Swatinem/rust-cache@v2
      - name: Install cargo-audit
        run: cargo install cargo-audit
      - name: Run security audit
        run: cargo audit

  clippy-security:
    name: Clippy Security Lints
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - uses: dtolnay/rust-toolchain@stable
        with:
          components: clippy
      - uses: Swatinem/rust-cache@v2
      - name: Run security-focused clippy lints
        run: |
          cargo clippy --workspace --all-features -- \
            -W clippy::integer_arithmetic \
            -W clippy::panic \
            -W clippy::unwrap_used \
            -W clippy::expect_used \
            -W clippy::indexing_slicing \
            -W clippy::unreachable \
            -W clippy::todo \
            -W clippy::unimplemented
