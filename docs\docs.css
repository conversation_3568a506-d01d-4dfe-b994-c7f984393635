/* ===== DOCUMENTATION STYLES ===== */

/* Documentation Layout */
.docs-main {
  min-height: 100vh;
  padding-top: var(--navbar-height);
  background: var(--bg-primary);
}

.docs-navbar {
  background: rgba(var(--bg-primary-rgb), 0.95);
  backdrop-filter: blur(20px);
  border-bottom: 1px solid var(--border-color);
}

/* Breadcrumb Navigation */
.breadcrumb-container {
  background: var(--bg-secondary);
  border-bottom: 1px solid var(--border-color);
  padding: var(--space-4) 0;
}

.breadcrumb-list {
  display: flex;
  align-items: center;
  gap: var(--space-2);
  list-style: none;
  margin: 0;
  padding: 0;
}

.breadcrumb-item {
  display: flex;
  align-items: center;
  font-size: 14px;
  color: var(--text-secondary);
}

.breadcrumb-item:not(:last-child)::after {
  content: '/';
  margin-left: var(--space-2);
  color: var(--text-tertiary);
}

.breadcrumb-link {
  color: var(--text-secondary);
  text-decoration: none;
  transition: color 0.2s ease;
}

.breadcrumb-link:hover {
  color: var(--accent-primary);
}

.breadcrumb-item.active {
  color: var(--text-primary);
  font-weight: 500;
}

/* Documentation Header */
.docs-header {
  background: linear-gradient(135deg, var(--bg-secondary) 0%, var(--bg-primary) 100%);
  padding: var(--space-12) 0;
  border-bottom: 1px solid var(--border-color);
}

.docs-header-content {
  text-align: center;
  max-width: 800px;
  margin: 0 auto;
}

.docs-meta {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: var(--space-4);
  margin-bottom: var(--space-4);
  flex-wrap: wrap;
}

.docs-category {
  background: var(--accent-primary);
  color: white;
  padding: var(--space-1) var(--space-3);
  border-radius: var(--radius-full);
  font-size: 12px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.docs-level {
  padding: var(--space-1) var(--space-3);
  border-radius: var(--radius-full);
  font-size: 12px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.docs-level.beginner {
  background: var(--success-bg);
  color: var(--success-text);
}

.docs-level.intermediate {
  background: var(--warning-bg);
  color: var(--warning-text);
}

.docs-level.advanced {
  background: var(--error-bg);
  color: var(--error-text);
}

.docs-level.expert {
  background: var(--accent-secondary);
  color: white;
}

.docs-time {
  color: var(--text-secondary);
  font-size: 14px;
}

.docs-title {
  font-size: clamp(2rem, 4vw, 3rem);
  font-weight: 700;
  color: var(--text-primary);
  margin-bottom: var(--space-4);
  line-height: 1.2;
}

.docs-subtitle {
  font-size: 18px;
  color: var(--text-secondary);
  line-height: 1.6;
  margin-bottom: var(--space-6);
}

.docs-actions {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: var(--space-4);
}

.bookmark-btn {
  background: var(--bg-tertiary);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-md);
  padding: var(--space-2);
  color: var(--text-secondary);
  cursor: pointer;
  transition: all 0.2s ease;
}

.bookmark-btn:hover {
  background: var(--accent-primary);
  color: white;
  border-color: var(--accent-primary);
}

.bookmark-btn svg {
  width: 20px;
  height: 20px;
}

.reading-progress {
  width: 200px;
  height: 4px;
  background: var(--bg-tertiary);
  border-radius: var(--radius-full);
  overflow: hidden;
}

.progress-bar {
  height: 100%;
  background: linear-gradient(90deg, var(--accent-primary), var(--accent-secondary));
  width: 0%;
  transition: width 0.3s ease;
}

/* Documentation Content Layout */
.docs-content {
  padding: var(--space-12) 0;
}

.docs-layout {
  display: grid;
  grid-template-columns: 280px 1fr;
  gap: var(--space-8);
  max-width: 1400px;
  margin: 0 auto;
}

/* Table of Contents Sidebar */
.docs-sidebar {
  position: sticky;
  top: calc(var(--navbar-height) + var(--space-4));
  height: fit-content;
}

.toc-container {
  padding: var(--space-6);
  border-radius: var(--radius-lg);
  border: 1px solid var(--border-color);
}

.toc-title {
  font-size: 16px;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: var(--space-4);
}

.toc-list {
  list-style: none;
  margin: 0;
  padding: 0;
}

.toc-item {
  margin-bottom: var(--space-2);
}

.toc-link {
  display: block;
  padding: var(--space-2) var(--space-3);
  color: var(--text-secondary);
  text-decoration: none;
  border-radius: var(--radius-md);
  font-size: 14px;
  transition: all 0.2s ease;
  border-left: 3px solid transparent;
}

.toc-link:hover {
  background: var(--bg-secondary);
  color: var(--text-primary);
  border-left-color: var(--accent-primary);
}

.toc-link.active {
  background: var(--accent-primary-light);
  color: var(--accent-primary);
  border-left-color: var(--accent-primary);
  font-weight: 500;
}

/* Main Article Content */
.docs-article {
  max-width: none;
  line-height: 1.7;
}

.docs-section {
  margin-bottom: var(--space-12);
}

.docs-section h2 {
  font-size: 28px;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: var(--space-6);
  padding-bottom: var(--space-3);
  border-bottom: 2px solid var(--border-color);
}

.docs-section h3 {
  font-size: 22px;
  font-weight: 600;
  color: var(--text-primary);
  margin: var(--space-8) 0 var(--space-4) 0;
}

.docs-section p {
  color: var(--text-secondary);
  margin-bottom: var(--space-4);
  font-size: 16px;
}

.docs-list {
  margin: var(--space-4) 0;
  padding-left: var(--space-6);
}

.docs-list li {
  color: var(--text-secondary);
  margin-bottom: var(--space-2);
  font-size: 16px;
}

.docs-list li strong {
  color: var(--text-primary);
}

/* Code Blocks */
.code-block {
  margin: var(--space-6) 0;
  border-radius: var(--radius-lg);
  overflow: hidden;
  border: 1px solid var(--border-color);
  background: var(--bg-code);
}

.code-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--space-3) var(--space-4);
  background: var(--bg-secondary);
  border-bottom: 1px solid var(--border-color);
}

.code-title {
  font-size: 14px;
  font-weight: 500;
  color: var(--text-primary);
}

.copy-btn {
  background: var(--bg-tertiary);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-sm);
  padding: var(--space-1) var(--space-2);
  color: var(--text-secondary);
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 12px;
}

.copy-btn:hover {
  background: var(--accent-primary);
  color: white;
  border-color: var(--accent-primary);
}

.copy-btn svg {
  width: 14px;
  height: 14px;
}

.code-block pre {
  margin: 0;
  padding: var(--space-4);
  overflow-x: auto;
  background: transparent;
}

.code-block code {
  font-family: 'JetBrains Mono', 'Fira Code', 'Consolas', monospace;
  font-size: 14px;
  line-height: 1.5;
}

/* Info Boxes */
.info-box,
.warning-box,
.success-box,
.error-box {
  display: flex;
  gap: var(--space-3);
  padding: var(--space-4);
  border-radius: var(--radius-lg);
  margin: var(--space-6) 0;
  border-left: 4px solid;
}

.info-box {
  background: var(--info-bg);
  border-left-color: var(--info-border);
}

.warning-box {
  background: var(--warning-bg);
  border-left-color: var(--warning-border);
}

.success-box {
  background: var(--success-bg);
  border-left-color: var(--success-border);
}

.error-box {
  background: var(--error-bg);
  border-left-color: var(--error-border);
}

.info-icon,
.warning-icon,
.success-icon,
.error-icon {
  flex-shrink: 0;
  width: 20px;
  height: 20px;
}

.info-icon {
  color: var(--info-text);
}

.warning-icon {
  color: var(--warning-text);
}

.success-icon {
  color: var(--success-text);
}

.error-icon {
  color: var(--error-text);
}

.info-content,
.warning-content,
.success-content,
.error-content {
  flex: 1;
}

.info-content strong,
.warning-content strong,
.success-content strong,
.error-content strong {
  display: block;
  margin-bottom: var(--space-1);
  font-weight: 600;
}

.info-content {
  color: var(--info-text);
}

.warning-content {
  color: var(--warning-text);
}

.success-content {
  color: var(--success-text);
}

.error-content {
  color: var(--error-text);
}

/* Next Steps Grid */
.next-steps-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: var(--space-6);
  margin-top: var(--space-8);
}

.next-step-card {
  padding: var(--space-6);
  border-radius: var(--radius-lg);
  border: 1px solid var(--border-color);
  text-align: center;
  transition: all 0.3s ease;
}

.next-step-card:hover {
  transform: translateY(-4px);
  box-shadow: var(--shadow-lg);
  border-color: var(--accent-primary);
}

.step-icon {
  width: 48px;
  height: 48px;
  margin: 0 auto var(--space-4) auto;
  color: var(--accent-primary);
}

.next-step-card h3 {
  font-size: 18px;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: var(--space-2);
}

.next-step-card p {
  color: var(--text-secondary);
  font-size: 14px;
  margin-bottom: var(--space-4);
}

.step-link {
  color: var(--accent-primary);
  text-decoration: none;
  font-weight: 500;
  font-size: 14px;
  transition: color 0.2s ease;
}

.step-link:hover {
  color: var(--accent-secondary);
}

/* Documentation Footer */
.docs-footer {
  background: var(--bg-secondary);
  border-top: 1px solid var(--border-color);
  padding: var(--space-8) 0;
  margin-top: var(--space-16);
}

.footer-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: var(--space-4);
}

.footer-section p {
  color: var(--text-secondary);
  font-size: 14px;
  margin: 0;
}

.footer-section a {
  color: var(--accent-primary);
  text-decoration: none;
}

.footer-section a:hover {
  text-decoration: underline;
}

.footer-links {
  display: flex;
  gap: var(--space-6);
}

.footer-links a {
  color: var(--text-secondary);
  text-decoration: none;
  font-size: 14px;
  transition: color 0.2s ease;
}

.footer-links a:hover {
  color: var(--accent-primary);
}

/* Responsive Design */
@media (max-width: 1024px) {
  .docs-layout {
    grid-template-columns: 1fr;
    gap: var(--space-6);
  }
  
  .docs-sidebar {
    position: static;
    order: -1;
  }
  
  .toc-container {
    padding: var(--space-4);
  }
}

@media (max-width: 768px) {
  .docs-header {
    padding: var(--space-8) 0;
  }
  
  .docs-meta {
    flex-direction: column;
    gap: var(--space-2);
  }
  
  .docs-title {
    font-size: 2rem;
  }
  
  .docs-subtitle {
    font-size: 16px;
  }
  
  .docs-content {
    padding: var(--space-8) 0;
  }
  
  .docs-section {
    margin-bottom: var(--space-8);
  }
  
  .docs-section h2 {
    font-size: 24px;
  }
  
  .next-steps-grid {
    grid-template-columns: 1fr;
  }
  
  .footer-content {
    flex-direction: column;
    text-align: center;
  }
}
