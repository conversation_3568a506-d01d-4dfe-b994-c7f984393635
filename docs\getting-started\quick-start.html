<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Quick Start Guide - Forge EC Documentation</title>
    
    <!-- SEO Meta Tags -->
    <meta name="description" content="Get up and running with Forge EC in under 5 minutes. Step-by-step tutorial for Rust elliptic curve cryptography.">
    <meta name="keywords" content="forge ec, rust, elliptic curve, cryptography, quick start, tutorial">
    <meta name="author" content="Tanmay Patil">
    
    <!-- Open Graph Meta Tags -->
    <meta property="og:title" content="Quick Start Guide - Forge EC Documentation">
    <meta property="og:description" content="Get up and running with Forge EC in under 5 minutes">
    <meta property="og:type" content="article">
    <meta property="og:url" content="https://forge-ec.dev/docs/getting-started/quick-start.html">
    
    <!-- Stylesheets -->
    <link rel="stylesheet" href="../../css/style.css">
    <link rel="stylesheet" href="../../css/animations.css">
    <link rel="stylesheet" href="../../css/components.css">
    <link rel="stylesheet" href="../docs.css">
    
    <!-- Favicon -->
    <link rel="icon" type="image/svg+xml" href="../../assets/favicon.svg">
    
    <!-- Syntax Highlighting -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/themes/prism-tomorrow.min.css">
</head>
<body>
    <!-- Loading Screen -->
    <div class="loading-screen" id="loading-screen">
        <div class="loading-content">
            <div class="loading-logo">
                <svg viewBox="0 0 40 40" xmlns="http://www.w3.org/2000/svg">
                    <path d="M8,20 Q20,8 32,20 Q20,32 8,20" stroke="currentColor" stroke-width="2" fill="none"/>
                    <circle cx="20" cy="20" r="2" fill="currentColor"/>
                </svg>
            </div>
            <div class="loading-text">Loading Documentation...</div>
            <div class="loading-progress">
                <div class="loading-bar" id="loading-bar"></div>
            </div>
        </div>
    </div>

    <!-- Navigation -->
    <nav class="navbar docs-navbar" id="navbar">
        <div class="nav-container">
            <div class="nav-brand">
                <a href="../../index.html" class="brand-link">
                    <svg class="brand-logo" viewBox="0 0 40 40" xmlns="http://www.w3.org/2000/svg">
                        <path d="M8,20 Q20,8 32,20 Q20,32 8,20" stroke="currentColor" stroke-width="2" fill="none"/>
                        <circle cx="20" cy="20" r="2" fill="currentColor"/>
                    </svg>
                    <span class="brand-text">Forge EC</span>
                </a>
            </div>

            <div class="nav-menu" id="nav-menu">
                <a href="../../index.html" class="nav-link">Home</a>
                <a href="../../index.html#features" class="nav-link">Features</a>
                <a href="../../index.html#about" class="nav-link">About</a>
                <a href="../../index.html#docs" class="nav-link active">Documentation</a>
                <a href="../../index.html#examples" class="nav-link">Examples</a>
                <a href="../../index.html#community" class="nav-link">Community</a>
                <a href="../../index.html#contact" class="nav-link">Contact</a>
            </div>

            <div class="nav-actions">
                <button class="theme-toggle" id="theme-toggle" aria-label="Toggle theme">
                    <svg class="theme-icon sun-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                        <circle cx="12" cy="12" r="5"/>
                        <path d="M12 1v2M12 21v2M4.22 4.22l1.42 1.42M18.36 18.36l1.42 1.42M1 12h2M21 12h2M4.22 19.78l1.42-1.42M18.36 5.64l1.42-1.42"/>
                    </svg>
                    <svg class="theme-icon moon-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                        <path d="M21 12.79A9 9 0 1 1 11.21 3 7 7 0 0 0 21 12.79z"/>
                    </svg>
                </button>
                <button class="auth-btn" id="auth-btn">
                    <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
                        <path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"/>
                        <circle cx="12" cy="7" r="4"/>
                    </svg>
                    <span>Sign In</span>
                </button>
                <button class="mobile-menu-toggle" id="mobile-menu-toggle" aria-label="Toggle mobile menu">
                    <span></span>
                    <span></span>
                    <span></span>
                </button>
            </div>
        </div>
    </nav>

    <!-- Documentation Content -->
    <main class="docs-main">
        <!-- Breadcrumb Navigation -->
        <div class="breadcrumb-container">
            <div class="container">
                <nav class="breadcrumb" aria-label="Breadcrumb">
                    <ol class="breadcrumb-list">
                        <li class="breadcrumb-item">
                            <a href="../../index.html#docs" class="breadcrumb-link">Documentation</a>
                        </li>
                        <li class="breadcrumb-item">
                            <a href="../getting-started.html" class="breadcrumb-link">Getting Started</a>
                        </li>
                        <li class="breadcrumb-item active" aria-current="page">Quick Start Guide</li>
                    </ol>
                </nav>
            </div>
        </div>

        <!-- Documentation Header -->
        <div class="docs-header">
            <div class="container">
                <div class="docs-header-content">
                    <div class="docs-meta">
                        <span class="docs-category">Getting Started</span>
                        <span class="docs-level beginner">Beginner</span>
                        <span class="docs-time">5 min read</span>
                    </div>
                    <h1 class="docs-title">Quick Start Guide</h1>
                    <p class="docs-subtitle">
                        Get up and running with Forge EC in under 5 minutes. This tutorial will walk you through 
                        installing the library and creating your first cryptographic operations.
                    </p>
                    <div class="docs-actions">
                        <button class="bookmark-btn" id="bookmark-btn" aria-label="Bookmark this page">
                            <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
                                <path d="M19 21l-7-5-7 5V5a2 2 0 0 1 2-2h10a2 2 0 0 1 2 2z"/>
                            </svg>
                        </button>
                        <div class="reading-progress">
                            <div class="progress-bar" id="reading-progress"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Documentation Content -->
        <div class="docs-content">
            <div class="container">
                <div class="docs-layout">
                    <!-- Table of Contents -->
                    <aside class="docs-sidebar">
                        <div class="toc-container glass-enhanced">
                            <h3 class="toc-title">Table of Contents</h3>
                            <nav class="toc" id="toc">
                                <ul class="toc-list">
                                    <li class="toc-item">
                                        <a href="#prerequisites" class="toc-link">Prerequisites</a>
                                    </li>
                                    <li class="toc-item">
                                        <a href="#installation" class="toc-link">Installation</a>
                                    </li>
                                    <li class="toc-item">
                                        <a href="#first-example" class="toc-link">Your First Example</a>
                                    </li>
                                    <li class="toc-item">
                                        <a href="#key-generation" class="toc-link">Key Generation</a>
                                    </li>
                                    <li class="toc-item">
                                        <a href="#signing" class="toc-link">Digital Signatures</a>
                                    </li>
                                    <li class="toc-item">
                                        <a href="#verification" class="toc-link">Signature Verification</a>
                                    </li>
                                    <li class="toc-item">
                                        <a href="#next-steps" class="toc-link">Next Steps</a>
                                    </li>
                                </ul>
                            </nav>
                        </div>
                    </aside>

                    <!-- Main Content -->
                    <article class="docs-article">
                        <section id="prerequisites" class="docs-section">
                            <h2>Prerequisites</h2>
                            <p>
                                Before getting started with Forge EC, ensure you have the following installed:
                            </p>
                            <ul class="docs-list">
                                <li><strong>Rust 1.70+</strong> - Install from <a href="https://rustup.rs/" target="_blank" rel="noopener">rustup.rs</a></li>
                                <li><strong>Cargo</strong> - Comes with Rust installation</li>
                                <li><strong>Git</strong> - For cloning repositories (optional)</li>
                            </ul>
                            
                            <div class="info-box">
                                <div class="info-icon">
                                    <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
                                        <circle cx="12" cy="12" r="10"/>
                                        <path d="M12 16v-4M12 8h.01"/>
                                    </svg>
                                </div>
                                <div class="info-content">
                                    <strong>Note:</strong> Forge EC requires Rust 1.70 or later for full feature support and optimal performance.
                                </div>
                            </div>
                        </section>

                        <section id="installation" class="docs-section">
                            <h2>Installation</h2>
                            <p>
                                Add Forge EC to your Rust project using Cargo:
                            </p>
                            
                            <div class="code-block">
                                <div class="code-header">
                                    <span class="code-title">Cargo.toml</span>
                                    <button class="copy-btn" data-copy='[dependencies]
forge-ec = "0.1.0"'>
                                        <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
                                            <rect x="9" y="9" width="13" height="13" rx="2" ry="2"/>
                                            <path d="M5 15H4a2 2 0 0 1-2-2V4a2 2 0 0 1 2-2h9a2 2 0 0 1 2 2v1"/>
                                        </svg>
                                    </button>
                                </div>
                                <pre><code class="language-toml">[dependencies]
forge-ec = "0.1.0"</code></pre>
                            </div>

                            <p>Or install using the command line:</p>
                            
                            <div class="code-block">
                                <div class="code-header">
                                    <span class="code-title">Terminal</span>
                                    <button class="copy-btn" data-copy="cargo add forge-ec">
                                        <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
                                            <rect x="9" y="9" width="13" height="13" rx="2" ry="2"/>
                                            <path d="M5 15H4a2 2 0 0 1-2-2V4a2 2 0 0 1 2-2h9a2 2 0 0 1 2 2v1"/>
                                        </svg>
                                    </button>
                                </div>
                                <pre><code class="language-bash">cargo add forge-ec</code></pre>
                            </div>
                        </section>

                        <section id="first-example" class="docs-section">
                            <h2>Your First Example</h2>
                            <p>
                                Let's create a simple example that demonstrates key generation and digital signatures:
                            </p>

                            <div class="code-block">
                                <div class="code-header">
                                    <span class="code-title">main.rs</span>
                                    <button class="copy-btn" data-copy='use forge_ec::ecdsa::*;
use forge_ec::curves::secp256k1::*;

fn main() -> Result<(), Box<dyn std::error::Error>> {
    println!("🦀 Welcome to Forge EC!");

    // Generate a new private key
    let private_key = PrivateKey::new();
    let public_key = private_key.public_key();

    println!("✅ Generated keypair successfully");

    // Message to sign
    let message = b"Hello, Forge EC!";

    // Sign the message
    let signature = private_key.sign(message)?;
    println!("✅ Message signed");

    // Verify the signature
    let is_valid = public_key.verify(message, &signature)?;
    println!("✅ Signature verification: {}", is_valid);

    Ok(())
}'>
                                        <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
                                            <rect x="9" y="9" width="13" height="13" rx="2" ry="2"/>
                                            <path d="M5 15H4a2 2 0 0 1-2-2V4a2 2 0 0 1 2-2h9a2 2 0 0 1 2 2v1"/>
                                        </svg>
                                    </button>
                                </div>
                                <pre><code class="language-rust">use forge_ec::ecdsa::*;
use forge_ec::curves::secp256k1::*;

fn main() -> Result<(), Box<dyn std::error::Error>> {
    println!("🦀 Welcome to Forge EC!");

    // Generate a new private key
    let private_key = PrivateKey::new();
    let public_key = private_key.public_key();

    println!("✅ Generated keypair successfully");

    // Message to sign
    let message = b"Hello, Forge EC!";

    // Sign the message
    let signature = private_key.sign(message)?;
    println!("✅ Message signed");

    // Verify the signature
    let is_valid = public_key.verify(message, &signature)?;
    println!("✅ Signature verification: {}", is_valid);

    Ok(())
}</code></pre>
                            </div>

                            <p>Run this example with:</p>

                            <div class="code-block">
                                <div class="code-header">
                                    <span class="code-title">Terminal</span>
                                    <button class="copy-btn" data-copy="cargo run">
                                        <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
                                            <rect x="9" y="9" width="13" height="13" rx="2" ry="2"/>
                                            <path d="M5 15H4a2 2 0 0 1-2-2V4a2 2 0 0 1 2-2h9a2 2 0 0 1 2 2v1"/>
                                        </svg>
                                    </button>
                                </div>
                                <pre><code class="language-bash">cargo run</code></pre>
                            </div>

                            <div class="success-box">
                                <div class="success-icon">
                                    <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
                                        <path d="M9 12l2 2 4-4M21 12c0 4.97-4.03 9-9 9s-9-4.03-9-9 4.03-9 9-9 9 4.03 9 9z"/>
                                    </svg>
                                </div>
                                <div class="success-content">
                                    <strong>Expected Output:</strong>
                                    <pre>🦀 Welcome to Forge EC!
✅ Generated keypair successfully
✅ Message signed
✅ Signature verification: true</pre>
                                </div>
                            </div>
                        </section>

                        <section id="key-generation" class="docs-section">
                            <h2>Key Generation</h2>
                            <p>
                                Forge EC provides secure key generation using cryptographically secure random number generators:
                            </p>

                            <div class="code-block">
                                <div class="code-header">
                                    <span class="code-title">Key Generation Example</span>
                                    <button class="copy-btn" data-copy='use forge_ec::ecdsa::PrivateKey;
use forge_ec::curves::secp256k1::Secp256k1;

// Generate a new random private key
let private_key = PrivateKey::<Secp256k1>::new();

// Derive the corresponding public key
let public_key = private_key.public_key();

// Export keys for storage (hex format)
let private_hex = private_key.to_hex();
let public_hex = public_key.to_hex();

println!("Private key: {}", private_hex);
println!("Public key: {}", public_hex);'>
                                        <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
                                            <rect x="9" y="9" width="13" height="13" rx="2" ry="2"/>
                                            <path d="M5 15H4a2 2 0 0 1-2-2V4a2 2 0 0 1 2-2h9a2 2 0 0 1 2 2v1"/>
                                        </svg>
                                    </button>
                                </div>
                                <pre><code class="language-rust">use forge_ec::ecdsa::PrivateKey;
use forge_ec::curves::secp256k1::Secp256k1;

// Generate a new random private key
let private_key = PrivateKey::<Secp256k1>::new();

// Derive the corresponding public key
let public_key = private_key.public_key();

// Export keys for storage (hex format)
let private_hex = private_key.to_hex();
let public_hex = public_key.to_hex();

println!("Private key: {}", private_hex);
println!("Public key: {}", public_hex);</code></pre>
                            </div>

                            <div class="warning-box">
                                <div class="warning-icon">
                                    <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
                                        <path d="M10.29 3.86L1.82 18a2 2 0 0 0 1.71 3h16.94a2 2 0 0 0 1.71-3L13.71 3.86a2 2 0 0 0-3.42 0z"/>
                                        <line x1="12" y1="9" x2="12" y2="13"/>
                                        <line x1="12" y1="17" x2="12.01" y2="17"/>
                                    </svg>
                                </div>
                                <div class="warning-content">
                                    <strong>Security Warning:</strong> Never share your private key or store it in plain text.
                                    Always use secure storage mechanisms in production applications.
                                </div>
                            </div>
                        </section>

                        <section id="signing" class="docs-section">
                            <h2>Digital Signatures</h2>
                            <p>
                                Create digital signatures to prove authenticity and integrity of your data:
                            </p>

                            <div class="code-block">
                                <div class="code-header">
                                    <span class="code-title">Signing Example</span>
                                    <button class="copy-btn" data-copy='use forge_ec::ecdsa::*;
use forge_ec::curves::secp256k1::*;

fn sign_message(private_key: &PrivateKey, message: &[u8]) -> Result<Signature, SignError> {
    // Hash the message (SHA-256 is used internally)
    let signature = private_key.sign(message)?;

    println!("Message signed successfully");
    println!("Signature: {}", signature.to_hex());

    Ok(signature)
}

// Usage
let private_key = PrivateKey::new();
let message = b"Important document content";
let signature = sign_message(&private_key, message)?;'>
                                        <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
                                            <rect x="9" y="9" width="13" height="13" rx="2" ry="2"/>
                                            <path d="M5 15H4a2 2 0 0 1-2-2V4a2 2 0 0 1 2-2h9a2 2 0 0 1 2 2v1"/>
                                        </svg>
                                    </button>
                                </div>
                                <pre><code class="language-rust">use forge_ec::ecdsa::*;
use forge_ec::curves::secp256k1::*;

fn sign_message(private_key: &PrivateKey, message: &[u8]) -> Result<Signature, SignError> {
    // Hash the message (SHA-256 is used internally)
    let signature = private_key.sign(message)?;

    println!("Message signed successfully");
    println!("Signature: {}", signature.to_hex());

    Ok(signature)
}

// Usage
let private_key = PrivateKey::new();
let message = b"Important document content";
let signature = sign_message(&private_key, message)?;</code></pre>
                            </div>
                        </section>

                        <section id="verification" class="docs-section">
                            <h2>Signature Verification</h2>
                            <p>
                                Verify digital signatures to ensure data authenticity:
                            </p>

                            <div class="code-block">
                                <div class="code-header">
                                    <span class="code-title">Verification Example</span>
                                    <button class="copy-btn" data-copy='use forge_ec::ecdsa::*;

fn verify_signature(
    public_key: &PublicKey,
    message: &[u8],
    signature: &Signature
) -> Result<bool, VerifyError> {
    let is_valid = public_key.verify(message, signature)?;

    if is_valid {
        println!("✅ Signature is valid - message is authentic");
    } else {
        println!("❌ Signature is invalid - message may be tampered");
    }

    Ok(is_valid)
}

// Usage
let is_authentic = verify_signature(&public_key, message, &signature)?;
assert!(is_authentic);'>
                                        <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
                                            <rect x="9" y="9" width="13" height="13" rx="2" ry="2"/>
                                            <path d="M5 15H4a2 2 0 0 1-2-2V4a2 2 0 0 1 2-2h9a2 2 0 0 1 2 2v1"/>
                                        </svg>
                                    </button>
                                </div>
                                <pre><code class="language-rust">use forge_ec::ecdsa::*;

fn verify_signature(
    public_key: &PublicKey,
    message: &[u8],
    signature: &Signature
) -> Result<bool, VerifyError> {
    let is_valid = public_key.verify(message, signature)?;

    if is_valid {
        println!("✅ Signature is valid - message is authentic");
    } else {
        println!("❌ Signature is invalid - message may be tampered");
    }

    Ok(is_valid)
}

// Usage
let is_authentic = verify_signature(&public_key, message, &signature)?;
assert!(is_authentic);</code></pre>
                            </div>
                        </section>

                        <section id="next-steps" class="docs-section">
                            <h2>Next Steps</h2>
                            <p>
                                Congratulations! You've successfully created your first Forge EC application. Here's what to explore next:
                            </p>

                            <div class="next-steps-grid">
                                <div class="next-step-card glass-enhanced">
                                    <div class="step-icon">
                                        <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
                                            <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"/>
                                            <polyline points="14,2 14,8 20,8"/>
                                        </svg>
                                    </div>
                                    <h3>Installation Guide</h3>
                                    <p>Learn about advanced installation options and feature flags</p>
                                    <a href="installation.html" class="step-link">Read Guide →</a>
                                </div>

                                <div class="next-step-card glass-enhanced">
                                    <div class="step-icon">
                                        <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
                                            <circle cx="12" cy="12" r="3"/>
                                            <path d="M19.4 15a1.65 1.65 0 0 0 .33 1.82l.06.06a2 2 0 0 1 0 2.83 2 2 0 0 1-2.83 0l-.06-.06a1.65 1.65 0 0 0-1.82-.33 1.65 1.65 0 0 0-1 1.51V21a2 2 0 0 1-2 2 2 2 0 0 1-2-2v-.09A1.65 1.65 0 0 0 9 19.4a1.65 1.65 0 0 0-1.82.33l-.06.06a2 2 0 0 1-2.83 0 2 2 0 0 1 0-2.83l.06-.06a1.65 1.65 0 0 0 .33-1.82 1.65 1.65 0 0 0-1.51-1H3a2 2 0 0 1-2-2 2 2 0 0 1 2-2h.09A1.65 1.65 0 0 0 4.6 9a1.65 1.65 0 0 0-.33-1.82l-.06-.06a2 2 0 0 1 0-2.83 2 2 0 0 1 2.83 0l.06.06a1.65 1.65 0 0 0 1.82.33H9a1.65 1.65 0 0 0 1-1.51V3a2 2 0 0 1 2-2 2 2 0 0 1 2 2v.09a1.65 1.65 0 0 0 1 1.51 1.65 1.65 0 0 0 1.82-.33l.06-.06a2 2 0 0 1 2.83 0 2 2 0 0 1 0 2.83l-.06.06a1.65 1.65 0 0 0-.33 1.82V9a1.65 1.65 0 0 0 1.51 1H21a2 2 0 0 1 2 2 2 2 0 0 1-2 2h-.09a1.65 1.65 0 0 0-1.51 1z"/>
                                        </svg>
                                    </div>
                                    <h3>Configuration</h3>
                                    <p>Customize Forge EC for your specific use case and requirements</p>
                                    <a href="configuration.html" class="step-link">Configure →</a>
                                </div>

                                <div class="next-step-card glass-enhanced">
                                    <div class="step-icon">
                                        <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
                                            <path d="M9 12l2 2 4-4M21 12c0 4.97-4.03 9-9 9s-9-4.03-9-9 4.03-9 9-9 9 4.03 9 9z"/>
                                        </svg>
                                    </div>
                                    <h3>API Reference</h3>
                                    <p>Explore the complete API documentation for all modules</p>
                                    <a href="../api/signatures.html" class="step-link">Browse API →</a>
                                </div>

                                <div class="next-step-card glass-enhanced">
                                    <div class="step-icon">
                                        <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
                                            <path d="M12 22s8-4 8-10V5l-8-3-8 3v7c0 6 8 10 8 10z"/>
                                        </svg>
                                    </div>
                                    <h3>Security Guidelines</h3>
                                    <p>Learn best practices for secure cryptographic implementations</p>
                                    <a href="../security/guidelines.html" class="step-link">Learn Security →</a>
                                </div>
                            </div>
                        </section>
                    </article>
                </div>
            </div>
        </div>
    </main>

    <!-- Footer -->
    <footer class="docs-footer">
        <div class="container">
            <div class="footer-content">
                <div class="footer-section">
                    <p>&copy; 2024 Forge EC. Built with ❤️ by <a href="https://github.com/tanm-sys" target="_blank">Tanmay Patil</a></p>
                </div>
                <div class="footer-section">
                    <div class="footer-links">
                        <a href="../../index.html#about">About</a>
                        <a href="../../index.html#community">Community</a>
                        <a href="../security/vulnerability-disclosure.html">Security</a>
                        <a href="https://github.com/tanm-sys/forge-ec" target="_blank">GitHub</a>
                    </div>
                </div>
            </div>
        </div>
    </footer>

    <!-- Firebase Configuration -->
    <script type="module">
        // Suppress browser extension errors that don't affect functionality
        const originalConsoleError = console.error;
        console.error = function(...args) {
            const message = args.join(' ');
            // Filter out known browser extension errors
            if (message.includes('message port closed') ||
                message.includes('Extension context invalidated') ||
                message.includes('runtime.lastError')) {
                return; // Suppress these harmless extension errors
            }
            originalConsoleError.apply(console, args);
        };

        try {
            // Firebase CDN imports with timeout protection
            const importTimeout = setTimeout(() => {
                console.warn('Firebase import timeout - continuing without Firebase');
                window.firebaseInitialized = false;
                window.dispatchEvent(new CustomEvent('firebaseReady'));
            }, 5000);

            Promise.all([
                import('https://www.gstatic.com/firebasejs/11.8.1/firebase-app.js'),
                import('https://www.gstatic.com/firebasejs/11.8.1/firebase-firestore.js'),
                import('https://www.gstatic.com/firebasejs/11.8.1/firebase-auth.js')
            ]).then(([appModule, firestoreModule, authModule]) => {
                clearTimeout(importTimeout);

                // Firebase configuration
                const firebaseConfig = {
                    apiKey: "AIzaSyDBG9YcnodA8Lhpwb3wOoyp93VcqXygcrQ",
                    authDomain: "forge-ec.firebaseapp.com",
                    databaseURL: "https://forge-ec-default-rtdb.firebaseio.com",
                    projectId: "forge-ec",
                    storageBucket: "forge-ec.firebasestorage.app",
                    messagingSenderId: "436060720516",
                    appId: "1:436060720516:web:4c4ac16371db82fcfd61d1",
                    measurementId: "G-1BVB7FLGRJ"
                };

                // Initialize Firebase
                const app = appModule.initializeApp(firebaseConfig);
                const db = firestoreModule.getFirestore(app);
                const auth = authModule.getAuth(app);

                // Make Firebase services globally available
                window.firebaseApp = app;
                window.firebaseDb = db;
                window.firebaseAuth = auth;
                window.firebaseInitialized = true;

                // Dispatch ready event
                window.dispatchEvent(new CustomEvent('firebaseReady'));
                console.log('✅ Firebase initialized successfully');
            }).catch(error => {
                clearTimeout(importTimeout);
                console.warn('Firebase initialization failed:', error);
                window.firebaseInitialized = false;
                window.dispatchEvent(new CustomEvent('firebaseReady'));
            });
        } catch (error) {
            console.warn('Firebase setup failed:', error);
            window.firebaseInitialized = false;
            window.dispatchEvent(new CustomEvent('firebaseReady'));
        }
    </script>

    <!-- Scripts -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/components/prism-core.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/plugins/autoloader/prism-autoloader.min.js"></script>
    <script src="../docs.js"></script>

    <!-- Unified Loading Screen Management -->
    <script>
        // Check if global LoadingScreenManager is available, otherwise use fallback
        function initializeLoadingScreenProtection() {
            // If docs.js LoadingScreenManager is available, it will handle everything
            if (window.LoadingScreenManager) {
                console.log('🔄 Using global LoadingScreenManager');
                return;
            }

            // Fallback protection if docs.js fails to load
            console.log('🔄 Using fallback loading screen protection');

            let fallbackHidden = false;
            function fallbackHide(reason) {
                if (fallbackHidden || window.loadingScreenHidden) return;

                const loadingScreen = document.getElementById('loading-screen');
                if (loadingScreen) {
                    console.log(`🔄 Fallback hiding loading screen: ${reason}`);
                    fallbackHidden = true;
                    window.loadingScreenHidden = true;

                    loadingScreen.style.opacity = '0';
                    loadingScreen.style.pointerEvents = 'none';
                    loadingScreen.style.transition = 'opacity 0.3s ease-out';

                    setTimeout(() => {
                        if (loadingScreen.parentNode) {
                            loadingScreen.style.display = 'none';
                            setTimeout(() => {
                                if (loadingScreen.parentNode) {
                                    loadingScreen.remove();
                                }
                            }, 100);
                        }
                    }, 300);
                }
            }

            // Fallback timeouts
            setTimeout(() => fallbackHide('Fallback timeout (3s)'), 3000);
            setTimeout(() => fallbackHide('Fallback timeout (6s)'), 6000);

            // Fallback event listeners
            window.addEventListener('load', () => setTimeout(() => fallbackHide('Fallback page load'), 500));
            document.addEventListener('DOMContentLoaded', () => setTimeout(() => fallbackHide('Fallback DOM loaded'), 1000));

            ['click', 'keydown', 'touchstart'].forEach(event => {
                document.addEventListener(event, () => fallbackHide('Fallback user interaction'), { once: true });
            });
        }

        // Initialize immediately
        initializeLoadingScreenProtection();

        // Also try after a short delay in case docs.js loads later
        setTimeout(initializeLoadingScreenProtection, 100);
    </script>
</body>
</html>
