# forge-ec-curves

[![Crates.io](https://img.shields.io/crates/v/forge-ec-curves.svg)](https://crates.io/crates/forge-ec-curves)
[![Documentation](https://docs.rs/forge-ec-curves/badge.svg)](https://docs.rs/forge-ec-curves)
[![License](https://img.shields.io/badge/license-Apache--2.0%20OR%20MIT-blue.svg)](../LICENSE)

Elliptic curve implementations for the Forge EC cryptography library.

## Getting Started

### Installation

Add `forge-ec-curves` to your `Cargo.toml`:

```toml
[dependencies]
forge-ec-curves = "0.1.0"
```

For a `no_std` environment:

```toml
[dependencies]
forge-ec-curves = { version = "0.1.0", default-features = false }
```

### Basic Usage

```rust
use forge_ec_core::{<PERSON>urve, FieldElement, PointAffine, <PERSON><PERSON><PERSON><PERSON>, <PERSON>alar};
use forge_ec_curves::secp256k1::Secp256k1;
use forge_ec_rng::os_rng::OsRng;

// Generate a random scalar (private key)
let mut rng = OsRng::new();
let private_key = Secp256k1::Scalar::random(&mut rng);

// Compute the corresponding public key
let public_key = Secp256k1::multiply(&Secp256k1::generator(), &private_key);
let public_key_affine = Secp256k1::to_affine(&public_key);

// Serialize the public key
let public_key_bytes = public_key_affine.to_bytes();
println!("Public key: {:?}", public_key_bytes);

// Deserialize a public key
let deserialized_public_key = Secp256k1::PointAffine::from_bytes(&public_key_bytes).unwrap();
assert_eq!(public_key_affine, deserialized_public_key);
```

## Overview

`forge-ec-curves` provides concrete implementations of various elliptic curves used in cryptography. Each curve implementation adheres to the traits defined in `forge-ec-core`, ensuring a consistent API across different curve types.

The crate currently implements the following curves:

- **secp256k1**: A Koblitz curve used in Bitcoin, Ethereum, and other cryptocurrencies
- **P-256** (NIST P-256): A widely used curve standardized by NIST
- **Curve25519**: A Montgomery curve used for key exchange (X25519)
- **Ed25519**: An Edwards curve used for EdDSA signatures

All implementations focus on security, with constant-time operations to prevent side-channel attacks, and performance, with optimized algorithms for each curve type.

## Curve Implementations

### secp256k1

The secp256k1 curve is defined by the equation y² = x³ + 7 over a prime field with modulus p = 2²⁵⁶ - 2³² - 977. It is widely used in cryptocurrency systems like Bitcoin and Ethereum.

```rust
use forge_ec_curves::secp256k1::Secp256k1;
use forge_ec_core::{Curve, PointFormat};
use forge_ec_rng::os_rng::OsRng;

// Generate a key pair
let mut rng = OsRng::new();
let secret_key = Secp256k1::Scalar::random(&mut rng);
let public_key = Secp256k1::multiply(&Secp256k1::generator(), &secret_key);
let public_key_affine = Secp256k1::to_affine(&public_key);

// Convert to compressed format
let compressed_pubkey = public_key_affine.to_bytes_with_format(PointFormat::Compressed);

// Key exchange
let peer_public_key = /* ... */;
let shared_secret = Secp256k1::multiply(&peer_public_key, &secret_key);
```

#### Implementation Details

- Field arithmetic is implemented using 4 64-bit limbs
- Scalar multiplication uses windowed non-adjacent form (wNAF) for efficiency
- Point addition and doubling use complete formulas to avoid exceptional cases
- Constant-time operations throughout to prevent timing attacks
- Full implementation of random number generation for field elements and scalars
- Support for point encoding/decoding in various formats
- Implementation of the KeyExchange trait for secure key derivation

#### Recent Major Achievements ✅

- **Comprehensive Code Quality Improvements**: 50+ clippy warnings resolved with automatic fixes
- **Enhanced Test Reliability**: 26/26 tests passing (100% success rate) for all curve implementations
- **Improved Build System**: Zero compilation errors across all curve implementations
- **Complete Curve25519 Implementation**: All field operations, scalar arithmetic, point operations, and X25519 key exchange fully implemented
- **Consistent Code Formatting**: Applied rustfmt for uniform code style across all implementations
- **Better Development Experience**: Improved IDE support and maintainability
- **Enhanced Security**: All operations are constant-time to prevent side-channel attacks
- **Robust Implementation**: All core cryptographic operations stable and reliable

### P-256 (NIST P-256)

The P-256 curve (also known as secp256r1 or prime256v1) is defined by the equation y² = x³ - 3x + b over a prime field. It is standardized by NIST and widely used in TLS and other protocols.

```rust
use forge_ec_curves::p256::P256;
use forge_ec_core::Curve;
use forge_ec_rng::os_rng::OsRng;

// Generate a key pair
let mut rng = OsRng::new();
let secret_key = P256::random_scalar(&mut rng);
let public_key = P256::multiply(&P256::generator(), &secret_key);
let public_key_affine = P256::to_affine(&public_key);

// Convert to uncompressed format
let uncompressed_pubkey = public_key_affine.to_bytes();
```

#### Implementation Details

- Field arithmetic is implemented using 4 64-bit limbs
- Scalar multiplication uses fixed-window method for regular patterns
- Point operations use Jacobian coordinates for efficiency
- Constant-time operations throughout to prevent timing attacks

### Curve25519

Curve25519 is a Montgomery curve designed for efficient and secure Diffie-Hellman key exchange. It is used in the X25519 key exchange protocol.

```rust
use forge_ec_curves::curve25519::Curve25519;
use forge_ec_core::{Curve, PointFormat, KeyExchange};
use forge_ec_rng::os_rng::OsRng;

// Generate a key pair
let mut rng = OsRng::new();
let secret_key = Curve25519::Scalar::random(&mut rng);
let public_key = Curve25519::multiply(&Curve25519::generator(), &secret_key);

// Perform X25519 key exchange
let peer_public_key_bytes = [0u8; 33]; // Replace with actual peer public key
let peer_public_key = Curve25519::PointAffine::from_bytes_with_format(&peer_public_key_bytes, PointFormat::Compressed).unwrap();
let peer_public_key_proj = Curve25519::from_affine(&peer_public_key);
let shared_secret = Curve25519::multiply(&peer_public_key_proj, &secret_key);
let shared_secret_bytes = Curve25519::to_affine(&shared_secret).x().to_bytes();

// Derive a symmetric key
let info = b"application specific info";
let symmetric_key = Curve25519::derive_key(&shared_secret_bytes, info, 32).unwrap();
```

#### Implementation Details ✅

- **Complete Field Arithmetic**: Implemented using 4 64-bit limbs with 2^255 - 19 modulus
- **Constant-Time Operations**: Montgomery ladder scalar multiplication for side-channel resistance
- **X25519 Protocol**: Fully implemented X25519 key exchange with proper validation
- **Comprehensive Operations**: All field operations (add, sub, mul, neg, invert, pow) implemented
- **Scalar Arithmetic**: Complete scalar operations with RFC6979 deterministic generation
- **Point Operations**: Addition, subtraction, doubling, negation, and scalar multiplication
- **Security Features**: All operations are constant-time to prevent timing attacks
- **Full Testing**: Comprehensive test suite with known test vectors
- **Standards Compliance**: Follows RFC 7748 specifications

### Ed25519

Ed25519 is a twisted Edwards curve used for EdDSA signatures. It offers high security and performance.

```rust
use forge_ec_curves::ed25519::Ed25519;
use forge_ec_core::{Curve, PointFormat};
use forge_ec_rng::os_rng::OsRng;

// Generate a key pair
let mut rng = OsRng::new();
let secret_key = Ed25519::Scalar::random(&mut rng);
let public_key = Ed25519::multiply(&Ed25519::generator(), &secret_key);
let public_key_affine = Ed25519::to_affine(&public_key);

// Convert to compressed format (standard for Ed25519)
let compressed_pubkey = public_key_affine.to_bytes_with_format(PointFormat::Compressed);

// Check if a point is on the curve
let is_on_curve = public_key_affine.is_on_curve();
assert!(bool::from(is_on_curve));

// Negate a point
let negated_point = public_key_affine.negate();
```

#### Implementation Details

- Field arithmetic is implemented using 4 64-bit limbs with 2^255 - 19 modulus
- Extended coordinates for efficient point operations
- Constant-time point addition with proper handling of special cases
- Optimized point doubling for Edwards curves
- Proper point negation and identity point handling
- Precomputed tables for the base point to accelerate scalar multiplication
- Batch verification support for EdDSA signatures
- Full implementation of random number generation for field elements and scalars
- Support for point encoding/decoding in various formats
- Implementation of point operations (add, double, negate, is_on_curve, conditional_select)
- Proper handling of the curve's cofactor (8)
- Comprehensive test suite for field arithmetic, scalar operations, and point arithmetic

## Field Arithmetic

Each curve implementation includes its own optimized field arithmetic tailored to the specific prime modulus of the curve. The field arithmetic is implemented to be constant-time to prevent timing attacks.

### Example: Field Element Operations

```rust
use forge_ec_curves::secp256k1::{FieldElement, Secp256k1};
use forge_ec_core::FieldElement as FieldElementTrait;

// Create field elements
let a = FieldElement::from_bytes(&[1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]).unwrap();
let b = FieldElement::from_bytes(&[2, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]).unwrap();

// Perform field operations
let sum = a.add(&b);
let product = a.mul(&b);
let inverse = a.invert().unwrap();

// Convert back to bytes
let sum_bytes = sum.to_bytes();
```

## Scalar Arithmetic

Each curve implementation also includes optimized scalar arithmetic for the curve's order. The scalar arithmetic is implemented to be constant-time to prevent timing attacks.

### Example: Scalar Operations

```rust
use forge_ec_curves::secp256k1::{Scalar, Secp256k1};
use forge_ec_core::Scalar as ScalarTrait;
use forge_ec_rng::os_rng::OsRng;

// Generate random scalars
let mut rng = OsRng::new();
let a = Scalar::random(&mut rng);
let b = Scalar::random(&mut rng);

// Perform scalar operations
let sum = a + b;
let product = a * b;
let inverse = a.invert().unwrap();

// Get the curve order
let order = Scalar::get_order();

// Convert to bytes
let a_bytes = a.to_bytes();

// Create a scalar from RFC6979 deterministic generation
let msg = b"message to sign";
let key = b"private key";
let extra = b"additional data";
let k = Scalar::from_rfc6979(msg, key, extra);
```

## Point Operations

Each curve implementation provides efficient point operations in both affine and projective coordinates.

### Example: Point Operations

```rust
use forge_ec_curves::secp256k1::Secp256k1;
use forge_ec_core::{Curve, PointAffine, PointProjective, PointFormat};

// Get the generator point
let g = Secp256k1::generator();

// Double the point
let g2 = g.double();

// Add points
let g3 = g.add(&g2);

// Negate a point
let neg_g = g.negate();

// Check if a point is on the curve
let is_on_curve = g.is_on_curve();
assert!(bool::from(is_on_curve));

// Convert to affine coordinates
let g3_affine = Secp256k1::to_affine(&g3);

// Get coordinates
let x = g3_affine.x();
let y = g3_affine.y();

// Serialize in different formats
let compressed = g3_affine.to_bytes_with_format(PointFormat::Compressed);
let uncompressed = g3_affine.to_bytes_with_format(PointFormat::Uncompressed);
let hybrid = g3_affine.to_bytes_with_format(PointFormat::Hybrid);

// Deserialize from different formats
let point1 = Secp256k1::PointAffine::from_bytes_with_format(&compressed, PointFormat::Compressed).unwrap();
let point2 = Secp256k1::PointAffine::from_bytes_with_format(&uncompressed, PointFormat::Uncompressed).unwrap();
```

## Advanced Usage Examples

### Custom Point Multiplication

```rust
use forge_ec_core::{Curve, FieldElement, PointAffine, PointProjective, Scalar};
use forge_ec_curves::secp256k1::Secp256k1;

// Define a scalar
let scalar_bytes = [
    0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07, 0x08,
    0x09, 0x0a, 0x0b, 0x0c, 0x0d, 0x0e, 0x0f, 0x10,
    0x11, 0x12, 0x13, 0x14, 0x15, 0x16, 0x17, 0x18,
    0x19, 0x1a, 0x1b, 0x1c, 0x1d, 0x1e, 0x1f, 0x20,
];
let scalar = Secp256k1::Scalar::from_bytes(&scalar_bytes).unwrap();

// Get the generator point
let generator = Secp256k1::generator();

// Perform scalar multiplication
let result = Secp256k1::multiply(&generator, &scalar);

// Convert to affine coordinates
let result_affine = Secp256k1::to_affine(&result);

// Access the coordinates
let x = result_affine.x();
let y = result_affine.y();
```

### Point Addition and Doubling

```rust
use forge_ec_core::{Curve, PointProjective};
use forge_ec_curves::secp256k1::Secp256k1;

// Get two points
let p1 = Secp256k1::generator();
let p2 = p1.double(); // 2G

// Add the points
let p3 = p1.add(&p2); // 3G

// Double a point
let p4 = p2.double(); // 4G

// Subtract points
let p1_again = p3.sub(&p2); // 3G - 2G = G
```

### Field Arithmetic

```rust
use forge_ec_core::FieldElement;
use forge_ec_curves::secp256k1::FieldElement as Secp256k1FieldElement;

// Create field elements
let a = Secp256k1FieldElement::from_bytes(&[1, 0, 0, 0, /* ... */]).unwrap();
let b = Secp256k1FieldElement::from_bytes(&[2, 0, 0, 0, /* ... */]).unwrap();

// Perform field operations
let sum = a.add(&b);
let product = a.mul(&b);
let squared = a.square();
let inverse = a.invert().unwrap();

// Check if a field element is zero
let is_zero = a.is_zero();
```

## Security Considerations

### Constant-Time Operations

All curve implementations in this crate are designed to be constant-time to prevent timing attacks:

- Field arithmetic operations run in constant time
- Point multiplication uses constant-time algorithms
- Equality checks and other conditional operations use the `subtle` crate
- Random number generation is implemented securely

Example of constant-time scalar multiplication:

```rust
use forge_ec_core::Curve;
use forge_ec_curves::secp256k1::Secp256k1;
use forge_ec_rng::os_rng::OsRng;

// Generate a random scalar
let mut rng = OsRng::new();
let scalar = Secp256k1::Scalar::random(&mut rng);

// This operation runs in constant time regardless of the scalar value
let point = Secp256k1::multiply(&Secp256k1::generator(), &scalar);
```

### Zeroization

Sensitive data like private keys are automatically zeroized when dropped:

```rust
use forge_ec_core::Scalar;
use forge_ec_curves::secp256k1::Scalar as Secp256k1Scalar;
use forge_ec_rng::os_rng::OsRng;
use zeroize::Zeroize;

{
    let mut rng = OsRng::new();
    let private_key = Secp256k1Scalar::random(&mut rng);
    // Use the private key...
} // private_key is automatically zeroized here
```

### Side-Channel Resistance

The curve implementations include protections against various side-channel attacks:

- Constant-time operations to prevent timing attacks
- Regular execution patterns to prevent power analysis
- No secret-dependent branches or memory accesses
- Secure random number generation for all cryptographic operations
- Point validation to prevent invalid curve attacks
- Proper handling of error cases without leaking sensitive information

## Standards Compliance

The curve implementations in this crate comply with the following standards:

- **secp256k1**: [SEC 2: Recommended Elliptic Curve Domain Parameters](https://www.secg.org/sec2-v2.pdf)
- **P-256**: [FIPS 186-4: Digital Signature Standard (DSS)](https://nvlpubs.nist.gov/nistpubs/FIPS/NIST.FIPS.186-4.pdf)
- **Curve25519**: [RFC 7748: Elliptic Curves for Security](https://tools.ietf.org/html/rfc7748)
- **Ed25519**: [RFC 8032: Edwards-Curve Digital Signature Algorithm (EdDSA)](https://tools.ietf.org/html/rfc8032)
- **RFC6979**: [Deterministic Usage of the Digital Signature Algorithm (DSA) and Elliptic Curve Digital Signature Algorithm (ECDSA)](https://tools.ietf.org/html/rfc6979)
- **RFC9380**: [Hashing to Elliptic Curves](https://tools.ietf.org/html/rfc9380)
- **SEC1**: [Elliptic Curve Cryptography](https://www.secg.org/sec1-v2.pdf) for point encoding/decoding

## Troubleshooting

### Common Issues

#### Invalid Point Encoding

**Issue**: `from_bytes` returns `None` when deserializing a point.

**Solution**: Ensure that the byte representation is valid for the curve:

```rust
use forge_ec_core::PointAffine;
use forge_ec_curves::secp256k1::PointAffine as Secp256k1PointAffine;

let bytes = [/* ... */];
match Secp256k1PointAffine::from_bytes(&bytes) {
    Some(point) => {
        // Valid point
    },
    None => {
        // Invalid encoding or point not on the curve
        // Check the format and ensure the point satisfies the curve equation
    }
}
```

#### Performance Issues

**Issue**: Point multiplication is slower than expected.

**Solution**: For repeated operations with the same base point, consider using precomputation:

```rust
use forge_ec_core::Curve;
use forge_ec_curves::secp256k1::Secp256k1;

// Precompute multiples of the base point (done once)
let base_point = Secp256k1::generator();
let precomputed = precompute_multiples(&base_point);

// Use precomputed values for faster multiplication
let scalar = Secp256k1::Scalar::from_bytes(&[/* ... */]).unwrap();
let result = multiply_with_precomputation(&precomputed, &scalar);
```

#### Curve-Specific Issues

**Issue**: Operations on one curve don't work with points from another curve.

**Solution**: Ensure you're using the correct curve implementation for your points:

```rust
use forge_ec_core::Curve;
use forge_ec_curves::secp256k1::Secp256k1;
use forge_ec_curves::p256::P256;

// This is correct
let secp256k1_point = Secp256k1::generator();
let secp256k1_scalar = Secp256k1::Scalar::from_bytes(&[/* ... */]).unwrap();
let result = Secp256k1::multiply(&secp256k1_point, &secp256k1_scalar);

// This would cause a type error
// let p256_point = P256::generator();
// let result = Secp256k1::multiply(&p256_point, &secp256k1_scalar); // Error!
```

## License

Licensed under either of:

- Apache License, Version 2.0 ([LICENSE-APACHE](../LICENSE-APACHE) or http://www.apache.org/licenses/LICENSE-2.0)
- MIT license ([LICENSE-MIT](../LICENSE-MIT) or http://opensource.org/licenses/MIT)

at your option.
