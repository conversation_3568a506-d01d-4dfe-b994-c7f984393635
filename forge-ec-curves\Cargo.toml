[package]
name = "forge-ec-curves"
version.workspace = true
authors.workspace = true
edition.workspace = true
rust-version.workspace = true
license.workspace = true
repository.workspace = true
documentation.workspace = true
homepage.workspace = true
description = "Elliptic curve implementations for the forge-ec library"
categories.workspace = true
keywords.workspace = true

[dependencies]
forge-ec-core = { workspace = true }
subtle = { workspace = true }
zeroize = { workspace = true }
rand_core = { workspace = true }
hex-literal = { version = "0.4", optional = true }
digest = { workspace = true }
rayon = { workspace = true, optional = true }
sha2 = { workspace = true }
hmac = { workspace = true }

# Optional SIMD support
cfg-if = "1.0"

[target.'cfg(any(target_arch = "x86", target_arch = "x86_64"))'.dependencies]
cpufeatures = "0.2"

[features]
default = ["std"]
std = ["forge-ec-core/std"]
alloc = ["forge-ec-core/alloc"]
simd = ["rayon"]  # Enable SIMD optimizations
test-utils = ["hex-literal"]