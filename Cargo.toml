[workspace]
members = [
    "p256-test",
    "forge-ec-core",
    "forge-ec-curves",
    "forge-ec-signature",
    "forge-ec-encoding",
    "forge-ec-hash",
    "forge-ec-rng",
]
resolver = "2"

[workspace.package]
version = "0.1.0"
authors = ["Forge EC Contributors"]
edition = "2021"
rust-version = "1.71.0"
license = "Apache-2.0 OR MIT"
repository = "https://github.com/forge-ec/forge-ec"
homepage = "https://github.com/forge-ec/forge-ec"
documentation = "https://docs.rs/forge-ec"
categories = ["cryptography", "no-std"]
keywords = ["crypto", "ecc", "elliptic-curves", "cryptography", "ecdsa"]

[workspace.dependencies]
# Core dependencies
subtle = "2.6.0"
zeroize = { version = "1.6.0", features = ["zeroize_derive"], default-features = false }
rand_core = { version = "0.6.4", default-features = false, features = ["getrandom"] }
digest = { version = "0.10", default-features = false }
sha2 = { version = "0.10", default-features = false }
hmac = { version = "0.12", default-features = false }

# Dependencies that may be used optionally by crates
rayon = { version = "1.8", default-features = false }
base58 = { version = "0.2", default-features = false }

# Internal crates
forge-ec-core = { path = "forge-ec-core", version = "0.1.0" }
forge-ec-curves = { path = "forge-ec-curves", version = "0.1.0" }
forge-ec-signature = { path = "forge-ec-signature", version = "0.1.0" }
forge-ec-encoding = { path = "forge-ec-encoding", version = "0.1.0" }
forge-ec-hash = { path = "forge-ec-hash", version = "0.1.0" }
forge-ec-rng = { path = "forge-ec-rng", version = "0.1.0" }

# Dev dependencies
criterion = "0.6"
proptest = "1.4"
hex = "0.4"
hex-literal = "0.4"

[profile.release]
opt-level = 3
lto = "thin"
debug = false
strip = "debuginfo"
codegen-units = 1

[profile.dev]
opt-level = 2
debug = true

[profile.test]
opt-level = 2
debug = true