[package]
name = "forge-ec-signature"
version.workspace = true
authors.workspace = true
edition.workspace = true
rust-version.workspace = true
license.workspace = true
repository.workspace = true
documentation.workspace = true
homepage.workspace = true
description = "Digital signature schemes for the forge-ec elliptic curve cryptography library"
categories.workspace = true
keywords.workspace = true

[dependencies]
forge-ec-core = { workspace = true }
forge-ec-curves = { workspace = true }
forge-ec-hash = { workspace = true }
forge-ec-rng = { workspace = true }
subtle = { workspace = true }
zeroize = { workspace = true }
rand_core = { workspace = true }
digest = { workspace = true }
hmac = { version = "0.12", default-features = false }
sha2 = { version = "0.10", default-features = false }
hex = { version = "0.4" }

[features]
default = ["std"]
std = ["forge-ec-core/std"]
alloc = ["forge-ec-core/alloc"]
test-utils = []